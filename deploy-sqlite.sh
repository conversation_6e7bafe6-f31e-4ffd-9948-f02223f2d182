#!/bin/bash
# SQLite部署脚本 - 兼容当前开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
SQLite部署脚本 - 适用于开发和测试环境

用法: $0 [选项] <操作> [环境]

操作:
  deploy    构建并部署服务
  build     仅构建镜像
  start     启动服务
  stop      停止服务
  restart   重启服务
  status    查看服务状态
  logs      查看服务日志
  clean     清理环境

环境:
  dev       开发环境 (默认)
  test      测试环境
  prod      生产环境 (使用SQLite)

选项:
  -h, --help     显示帮助信息
  --no-cache     构建时不使用缓存
  --pull         构建前拉取最新基础镜像

示例:
  $0 deploy dev          # 部署开发环境
  $0 build --no-cache    # 无缓存构建
  $0 logs backend        # 查看后端日志
EOF
}

# 检查环境文件
check_env_file() {
    local env=$1
    local env_file=".env.sqlite.${env}"
    
    if [[ ! -f "$env_file" ]]; then
        log_warning "环境文件 $env_file 不存在，创建默认配置..."
        create_env_file "$env"
    fi
}

# 创建环境文件
create_env_file() {
    local env=$1
    local env_file=".env.sqlite.${env}"
    
    cat > "$env_file" << EOF
# SQLite ${env} 环境配置

# 数据库配置 - SQLite
DATABASE_URL=sqlite:///./data/zentao_mcp_${env}.db

# 应用配置
SECRET_KEY=change_me_in_production_$(date +%s)
DEBUG=$([[ "$env" == "dev" ]] && echo "true" || echo "false")
LOG_LEVEL=$([[ "$env" == "dev" ]] && echo "DEBUG" || echo "INFO")

# 禅道API配置
ZENTAO_ENV=beta
# ZENTAO_BASE_URL=http://your-zentao-server.com

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123_${env}
ADMIN_EMAIL=<EMAIL>

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
EOF
    
    log_success "已创建环境文件: $env_file"
}

# 构建镜像
build_images() {
    local env=$1
    local build_args=""
    
    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi
    
    if [[ "$PULL" == "true" ]]; then
        build_args="$build_args --pull"
    fi
    
    log_info "构建 $env 环境镜像..."
    
    # 使用SQLite Dockerfile
    docker build $build_args \
        -f zentao-mcp-backend-service/Dockerfile.sqlite \
        -t zentao-mcp-backend:sqlite-$env \
        zentao-mcp-backend-service/
    
    log_success "镜像构建完成"
}

# 部署服务
deploy_services() {
    local env=$1
    
    log_info "部署 $env 环境服务 (SQLite)..."
    
    # 使用SQLite compose配置
    docker-compose -f docker-compose.sqlite.yml \
        --env-file ".env.sqlite.${env}" \
        up -d
    
    log_success "服务部署完成"
}

# 启动服务
start_services() {
    local env=$1
    
    log_info "启动 $env 环境服务..."
    
    docker-compose -f docker-compose.sqlite.yml \
        --env-file ".env.sqlite.${env}" \
        start
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    local env=$1
    
    log_info "停止 $env 环境服务..."
    
    docker-compose -f docker-compose.sqlite.yml \
        --env-file ".env.sqlite.${env}" \
        stop
    
    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    local env=$1
    
    log_info "重启 $env 环境服务..."
    
    docker-compose -f docker-compose.sqlite.yml \
        --env-file ".env.sqlite.${env}" \
        restart
    
    log_success "服务重启完成"
}

# 查看服务状态
show_status() {
    local env=$1
    
    log_info "查看 $env 环境服务状态..."
    
    docker-compose -f docker-compose.sqlite.yml \
        --env-file ".env.sqlite.${env}" \
        ps
}

# 查看日志
show_logs() {
    local env=$1
    local service=$2
    
    log_info "查看 $env 环境日志..."
    
    if [[ -n "$service" ]]; then
        docker-compose -f docker-compose.sqlite.yml \
            --env-file ".env.sqlite.${env}" \
            logs -f "$service"
    else
        docker-compose -f docker-compose.sqlite.yml \
            --env-file ".env.sqlite.${env}" \
            logs -f
    fi
}

# 清理环境
clean_environment() {
    local env=$1
    
    log_info "清理 $env 环境..."
    
    docker-compose -f docker-compose.sqlite.yml \
        --env-file ".env.sqlite.${env}" \
        down --volumes --remove-orphans || true
    
    # 清理镜像
    docker rmi zentao-mcp-backend:sqlite-$env || true
    
    log_success "环境清理完成"
}

# 主函数
main() {
    local operation=""
    local environment="dev"
    local service=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --no-cache)
                NO_CACHE="true"
                shift
                ;;
            --pull)
                PULL="true"
                shift
                ;;
            deploy|build|start|stop|restart|status|logs|clean)
                operation="$1"
                shift
                ;;
            dev|test|prod)
                environment="$1"
                shift
                ;;
            backend|frontend)
                service="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查操作
    if [[ -z "$operation" ]]; then
        log_error "请指定操作"
        show_help
        exit 1
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装或不在PATH中"
        exit 1
    fi
    
    # 检查环境文件
    check_env_file "$environment"
    
    # 执行操作
    case $operation in
        deploy)
            build_images "$environment"
            deploy_services "$environment"
            show_status "$environment"
            ;;
        build)
            build_images "$environment"
            ;;
        start)
            start_services "$environment"
            ;;
        stop)
            stop_services "$environment"
            ;;
        restart)
            restart_services "$environment"
            ;;
        status)
            show_status "$environment"
            ;;
        logs)
            show_logs "$environment" "$service"
            ;;
        clean)
            clean_environment "$environment"
            ;;
        *)
            log_error "未知操作: $operation"
            show_help
            exit 1
            ;;
    esac
    
    log_success "操作完成!"
}

# 执行主函数
main "$@"
