# 部署方式对比与选择指南

## 🚨 当前问题总结

### 发现的配置不匹配问题：

1. **Docker配置 vs 开发环境**
   - Docker: PostgreSQL 数据库
   - 开发环境: SQLite 数据库

2. **依赖管理不一致**
   - pyproject.toml: 包含PostgreSQL驱动
   - Dockerfile: 引用不存在的requirements.txt

3. **部署脚本假设**
   - 所有部署脚本都假设使用PostgreSQL
   - 没有SQLite部署选项

## ✅ 修复方案对比

| 方案 | 适用场景 | 优点 | 缺点 | 推荐度 |
|------|----------|------|------|--------|
| **SQLite Docker** | 开发/测试 | 简单、快速、无外部依赖 | 不支持高并发 | ⭐⭐⭐⭐ |
| **PostgreSQL Docker** | 生产环境 | 高性能、支持并发 | 复杂、需要数据迁移 | ⭐⭐⭐⭐⭐ |
| **混合方案** | 全场景 | 灵活选择 | 维护复杂 | ⭐⭐⭐ |

## 🚀 部署选择指南

### 1. 开发环境 (推荐SQLite)
```bash
# 使用SQLite部署脚本
./deploy-sqlite.sh deploy dev
```

### 2. 测试环境 (推荐SQLite)
```bash
# 快速测试部署
./deploy-sqlite.sh deploy test
```

### 3. 生产环境 (推荐PostgreSQL)
```bash
# 使用原始PostgreSQL配置
./deploy-universal.sh deploy prod
```

## 📁 文件结构说明

### SQLite相关文件：
- `docker-compose.sqlite.yml` - SQLite Docker配置
- `Dockerfile.sqlite` - SQLite优化的Dockerfile
- `requirements.txt` - SQLite兼容的依赖
- `deploy-sqlite.sh` - SQLite部署脚本
- `.env.sqlite.*` - SQLite环境配置

### PostgreSQL相关文件：
- `docker-compose.yml` - PostgreSQL Docker配置
- `Dockerfile` - 原始Dockerfile
- `pyproject.toml` - 包含PostgreSQL依赖
- `deploy-universal.sh` - PostgreSQL部署脚本

## 🔄 迁移路径

### 从开发(SQLite) → 生产(PostgreSQL)：

1. **数据导出**：
```bash
sqlite3 data/zentao_mcp.db .dump > backup.sql
```

2. **数据转换**：
```bash
# 使用工具转换SQL语法
# 或手动调整PostgreSQL兼容性
```

3. **部署PostgreSQL**：
```bash
./deploy-universal.sh deploy prod
```

4. **数据导入**：
```bash
# 在PostgreSQL容器中导入数据
```

## ⚠️ 注意事项

1. **数据持久化**：
   - SQLite: 数据文件需要挂载到宿主机
   - PostgreSQL: 使用Docker卷管理

2. **性能考虑**：
   - SQLite: 适合 < 100 并发用户
   - PostgreSQL: 适合高并发生产环境

3. **备份策略**：
   - SQLite: 直接复制数据库文件
   - PostgreSQL: 使用pg_dump等工具

4. **监控需求**：
   - SQLite: 基本文件系统监控
   - PostgreSQL: 需要数据库性能监控
