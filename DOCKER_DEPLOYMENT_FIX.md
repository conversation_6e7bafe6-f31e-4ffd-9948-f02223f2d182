# SQLite Docker部署指南

## 🚨 问题说明

发现Docker部署配置与当前SQLite数据库不匹配：

### 问题列表
1. **数据库不匹配**: Docker配置使用PostgreSQL，但开发环境使用SQLite
2. **依赖文件缺失**: Dockerfile引用不存在的requirements.txt
3. **依赖不一致**: pyproject.toml包含PostgreSQL驱动但使用SQLite

## ✅ 修复方案

### 1. SQLite Docker部署（推荐用于开发/测试）

```bash
# 使用SQLite配置部署
docker-compose -f docker-compose.sqlite.yml --env-file .env.sqlite up -d
```

### 2. PostgreSQL Docker部署（推荐用于生产）

如果要使用PostgreSQL，需要：

1. 更新应用配置：
```bash
# 修改.env文件
DATABASE_URL=*********************************************************/zentao_mcp
```

2. 运行数据库迁移：
```bash
# 在容器内执行
docker exec -it zentao-mcp-backend alembic upgrade head
```

3. 使用原始docker-compose：
```bash
docker-compose up -d
```

## 📁 新增文件

- `requirements.txt`: Docker构建依赖文件
- `docker-compose.sqlite.yml`: SQLite兼容的Docker配置
- `Dockerfile.sqlite`: SQLite优化的Dockerfile
- `.env.sqlite`: SQLite环境配置示例

## 🔄 迁移步骤

### 从SQLite迁移到PostgreSQL：

1. 导出SQLite数据：
```bash
sqlite3 data/zentao_mcp.db .dump > backup.sql
```

2. 启动PostgreSQL服务：
```bash
docker-compose up -d database
```

3. 导入数据到PostgreSQL（需要转换SQL语法）

### 从PostgreSQL迁移到SQLite：

1. 导出PostgreSQL数据
2. 转换为SQLite兼容格式
3. 导入到SQLite数据库

## 🚀 快速开始

### 开发环境（SQLite）：
```bash
# 1. 生成requirements.txt
python fix_docker_deployment.py

# 2. 构建并启动
docker-compose -f docker-compose.sqlite.yml --env-file .env.sqlite up -d

# 3. 检查状态
docker-compose -f docker-compose.sqlite.yml ps
```

### 生产环境（PostgreSQL）：
```bash
# 1. 配置环境变量
cp .env.example .env.prod
# 编辑 .env.prod 设置PostgreSQL连接

# 2. 启动服务
docker-compose -f docker-compose.yml -f docker-compose.prod.yml --env-file .env.prod up -d
```

## ⚠️ 注意事项

1. **数据持久化**: SQLite数据库文件需要挂载到宿主机
2. **并发限制**: SQLite不适合高并发生产环境
3. **备份策略**: 定期备份SQLite数据库文件
4. **性能考虑**: 生产环境建议使用PostgreSQL
