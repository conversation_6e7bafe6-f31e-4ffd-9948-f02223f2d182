#!/usr/bin/env python3
"""
修复所有部署配置中的数据库不匹配问题
"""

import os
import shutil
from pathlib import Path

def create_sqlite_deploy_script():
    """创建SQLite兼容的部署脚本"""
    
    print("🔧 创建SQLite兼容的部署脚本...")
    
    script_content = '''#!/bin/bash
# SQLite部署脚本 - 兼容当前开发环境

set -e

# 颜色定义
RED='\\033[0;31m'
GREEN='\\033[0;32m'
YELLOW='\\033[1;33m'
BLUE='\\033[0;34m'
NC='\\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
SQLite部署脚本 - 适用于开发和测试环境

用法: $0 [选项] <操作> [环境]

操作:
  deploy    构建并部署服务
  build     仅构建镜像
  start     启动服务
  stop      停止服务
  restart   重启服务
  status    查看服务状态
  logs      查看服务日志
  clean     清理环境

环境:
  dev       开发环境 (默认)
  test      测试环境
  prod      生产环境 (使用SQLite)

选项:
  -h, --help     显示帮助信息
  --no-cache     构建时不使用缓存
  --pull         构建前拉取最新基础镜像

示例:
  $0 deploy dev          # 部署开发环境
  $0 build --no-cache    # 无缓存构建
  $0 logs backend        # 查看后端日志
EOF
}

# 检查环境文件
check_env_file() {
    local env=$1
    local env_file=".env.sqlite.${env}"
    
    if [[ ! -f "$env_file" ]]; then
        log_warning "环境文件 $env_file 不存在，创建默认配置..."
        create_env_file "$env"
    fi
}

# 创建环境文件
create_env_file() {
    local env=$1
    local env_file=".env.sqlite.${env}"
    
    cat > "$env_file" << EOF
# SQLite ${env} 环境配置

# 数据库配置 - SQLite
DATABASE_URL=sqlite:///./data/zentao_mcp_${env}.db

# 应用配置
SECRET_KEY=change_me_in_production_$(date +%s)
DEBUG=$([[ "$env" == "dev" ]] && echo "true" || echo "false")
LOG_LEVEL=$([[ "$env" == "dev" ]] && echo "DEBUG" || echo "INFO")

# 禅道API配置
ZENTAO_ENV=beta
# ZENTAO_BASE_URL=http://your-zentao-server.com

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123_${env}
ADMIN_EMAIL=<EMAIL>

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
EOF
    
    log_success "已创建环境文件: $env_file"
}

# 构建镜像
build_images() {
    local env=$1
    local build_args=""
    
    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi
    
    if [[ "$PULL" == "true" ]]; then
        build_args="$build_args --pull"
    fi
    
    log_info "构建 $env 环境镜像..."
    
    # 使用SQLite Dockerfile
    docker build $build_args \\
        -f zentao-mcp-backend-service/Dockerfile.sqlite \\
        -t zentao-mcp-backend:sqlite-$env \\
        zentao-mcp-backend-service/
    
    log_success "镜像构建完成"
}

# 部署服务
deploy_services() {
    local env=$1
    
    log_info "部署 $env 环境服务 (SQLite)..."
    
    # 使用SQLite compose配置
    docker-compose -f docker-compose.sqlite.yml \\
        --env-file ".env.sqlite.${env}" \\
        up -d
    
    log_success "服务部署完成"
}

# 启动服务
start_services() {
    local env=$1
    
    log_info "启动 $env 环境服务..."
    
    docker-compose -f docker-compose.sqlite.yml \\
        --env-file ".env.sqlite.${env}" \\
        start
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    local env=$1
    
    log_info "停止 $env 环境服务..."
    
    docker-compose -f docker-compose.sqlite.yml \\
        --env-file ".env.sqlite.${env}" \\
        stop
    
    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    local env=$1
    
    log_info "重启 $env 环境服务..."
    
    docker-compose -f docker-compose.sqlite.yml \\
        --env-file ".env.sqlite.${env}" \\
        restart
    
    log_success "服务重启完成"
}

# 查看服务状态
show_status() {
    local env=$1
    
    log_info "查看 $env 环境服务状态..."
    
    docker-compose -f docker-compose.sqlite.yml \\
        --env-file ".env.sqlite.${env}" \\
        ps
}

# 查看日志
show_logs() {
    local env=$1
    local service=$2
    
    log_info "查看 $env 环境日志..."
    
    if [[ -n "$service" ]]; then
        docker-compose -f docker-compose.sqlite.yml \\
            --env-file ".env.sqlite.${env}" \\
            logs -f "$service"
    else
        docker-compose -f docker-compose.sqlite.yml \\
            --env-file ".env.sqlite.${env}" \\
            logs -f
    fi
}

# 清理环境
clean_environment() {
    local env=$1
    
    log_info "清理 $env 环境..."
    
    docker-compose -f docker-compose.sqlite.yml \\
        --env-file ".env.sqlite.${env}" \\
        down --volumes --remove-orphans || true
    
    # 清理镜像
    docker rmi zentao-mcp-backend:sqlite-$env || true
    
    log_success "环境清理完成"
}

# 主函数
main() {
    local operation=""
    local environment="dev"
    local service=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --no-cache)
                NO_CACHE="true"
                shift
                ;;
            --pull)
                PULL="true"
                shift
                ;;
            deploy|build|start|stop|restart|status|logs|clean)
                operation="$1"
                shift
                ;;
            dev|test|prod)
                environment="$1"
                shift
                ;;
            backend|frontend)
                service="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查操作
    if [[ -z "$operation" ]]; then
        log_error "请指定操作"
        show_help
        exit 1
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装或不在PATH中"
        exit 1
    fi
    
    # 检查环境文件
    check_env_file "$environment"
    
    # 执行操作
    case $operation in
        deploy)
            build_images "$environment"
            deploy_services "$environment"
            show_status "$environment"
            ;;
        build)
            build_images "$environment"
            ;;
        start)
            start_services "$environment"
            ;;
        stop)
            stop_services "$environment"
            ;;
        restart)
            restart_services "$environment"
            ;;
        status)
            show_status "$environment"
            ;;
        logs)
            show_logs "$environment" "$service"
            ;;
        clean)
            clean_environment "$environment"
            ;;
        *)
            log_error "未知操作: $operation"
            show_help
            exit 1
            ;;
    esac
    
    log_success "操作完成!"
}

# 执行主函数
main "$@"
'''
    
    script_path = Path("deploy-sqlite.sh")
    
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod(script_path, 0o755)
    
    print(f"✅ 已创建 {script_path}")

def update_docker_compose_sqlite():
    """更新SQLite docker-compose配置，使用正确的Dockerfile"""
    
    print("🔧 更新SQLite docker-compose配置...")
    
    # 读取现有配置
    sqlite_compose_path = Path("docker-compose.sqlite.yml")
    
    if sqlite_compose_path.exists():
        with open(sqlite_compose_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换Dockerfile路径
        content = content.replace(
            'dockerfile: Dockerfile',
            'dockerfile: Dockerfile.sqlite'
        )
        
        with open(sqlite_compose_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已更新 {sqlite_compose_path}")

def create_deployment_comparison():
    """创建部署方式对比文档"""
    
    print("🔧 创建部署方式对比文档...")
    
    comparison_content = """# 部署方式对比与选择指南

## 🚨 当前问题总结

### 发现的配置不匹配问题：

1. **Docker配置 vs 开发环境**
   - Docker: PostgreSQL 数据库
   - 开发环境: SQLite 数据库

2. **依赖管理不一致**
   - pyproject.toml: 包含PostgreSQL驱动
   - Dockerfile: 引用不存在的requirements.txt

3. **部署脚本假设**
   - 所有部署脚本都假设使用PostgreSQL
   - 没有SQLite部署选项

## ✅ 修复方案对比

| 方案 | 适用场景 | 优点 | 缺点 | 推荐度 |
|------|----------|------|------|--------|
| **SQLite Docker** | 开发/测试 | 简单、快速、无外部依赖 | 不支持高并发 | ⭐⭐⭐⭐ |
| **PostgreSQL Docker** | 生产环境 | 高性能、支持并发 | 复杂、需要数据迁移 | ⭐⭐⭐⭐⭐ |
| **混合方案** | 全场景 | 灵活选择 | 维护复杂 | ⭐⭐⭐ |

## 🚀 部署选择指南

### 1. 开发环境 (推荐SQLite)
```bash
# 使用SQLite部署脚本
./deploy-sqlite.sh deploy dev
```

### 2. 测试环境 (推荐SQLite)
```bash
# 快速测试部署
./deploy-sqlite.sh deploy test
```

### 3. 生产环境 (推荐PostgreSQL)
```bash
# 使用原始PostgreSQL配置
./deploy-universal.sh deploy prod
```

## 📁 文件结构说明

### SQLite相关文件：
- `docker-compose.sqlite.yml` - SQLite Docker配置
- `Dockerfile.sqlite` - SQLite优化的Dockerfile
- `requirements.txt` - SQLite兼容的依赖
- `deploy-sqlite.sh` - SQLite部署脚本
- `.env.sqlite.*` - SQLite环境配置

### PostgreSQL相关文件：
- `docker-compose.yml` - PostgreSQL Docker配置
- `Dockerfile` - 原始Dockerfile
- `pyproject.toml` - 包含PostgreSQL依赖
- `deploy-universal.sh` - PostgreSQL部署脚本

## 🔄 迁移路径

### 从开发(SQLite) → 生产(PostgreSQL)：

1. **数据导出**：
```bash
sqlite3 data/zentao_mcp.db .dump > backup.sql
```

2. **数据转换**：
```bash
# 使用工具转换SQL语法
# 或手动调整PostgreSQL兼容性
```

3. **部署PostgreSQL**：
```bash
./deploy-universal.sh deploy prod
```

4. **数据导入**：
```bash
# 在PostgreSQL容器中导入数据
```

## ⚠️ 注意事项

1. **数据持久化**：
   - SQLite: 数据文件需要挂载到宿主机
   - PostgreSQL: 使用Docker卷管理

2. **性能考虑**：
   - SQLite: 适合 < 100 并发用户
   - PostgreSQL: 适合高并发生产环境

3. **备份策略**：
   - SQLite: 直接复制数据库文件
   - PostgreSQL: 使用pg_dump等工具

4. **监控需求**：
   - SQLite: 基本文件系统监控
   - PostgreSQL: 需要数据库性能监控
"""
    
    comparison_path = Path("DEPLOYMENT_COMPARISON.md")
    
    with open(comparison_path, 'w', encoding='utf-8') as f:
        f.write(comparison_content)
    
    print(f"✅ 已创建 {comparison_path}")

def main():
    """主修复函数"""
    print("🚀 修复所有部署配置中的数据库不匹配问题...")
    print("=" * 60)
    
    # 1. 创建SQLite部署脚本
    create_sqlite_deploy_script()
    print()
    
    # 2. 更新docker-compose配置
    update_docker_compose_sqlite()
    print()
    
    # 3. 创建部署对比文档
    create_deployment_comparison()
    print()
    
    print("=" * 60)
    print("🎉 所有部署配置修复完成！")
    print()
    print("📋 新增/更新的文件:")
    print("  - deploy-sqlite.sh (SQLite部署脚本)")
    print("  - docker-compose.sqlite.yml (已更新)")
    print("  - DEPLOYMENT_COMPARISON.md (部署对比指南)")
    print()
    print("🚀 快速测试SQLite部署:")
    print("  ./deploy-sqlite.sh deploy dev")
    print()
    print("📖 查看完整指南:")
    print("  cat DEPLOYMENT_COMPARISON.md")

if __name__ == "__main__":
    main()
