version: '3.8'

services:
  # 后端服务 - SQLite版本
  backend:
    build:
      context: ./zentao-mcp-backend-service
      dockerfile: Dockerfile.sqlite
    container_name: zentao-mcp-backend-sqlite
    environment:
      - DATABASE_URL=sqlite:///./data/zentao_mcp.db
      - SECRET_KEY=your-secret-key-change-in-production
      - DEBUG=false
      - CORS_ORIGINS=http://localhost:3000
      - LOG_LEVEL=INFO
      - ZENTAO_ENV=beta
    ports:
      - "8000:8000"
    networks:
      - zentao-mcp-network
    restart: unless-stopped
    volumes:
      - backend_data:/app/data  # 持久化SQLite数据库
      - backend_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务（如果需要）
  frontend:
    build:
      context: ./zentao-mcp-frontend
      dockerfile: Dockerfile.sqlite
    container_name: zentao-mcp-frontend
    ports:
      - "3000:3000"
    networks:
      - zentao-mcp-network
    depends_on:
      - backend
    restart: unless-stopped
    environment:
      - REACT_APP_API_URL=http://backend:8000

# 网络配置
networks:
  zentao-mcp-network:
    driver: bridge

# 数据卷配置
volumes:
  backend_data:
    driver: local
  backend_logs:
    driver: local
