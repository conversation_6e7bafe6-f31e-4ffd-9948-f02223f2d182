#!/usr/bin/env python3
"""
修复Docker部署配置与SQLite数据库不匹配的问题
"""

import os
import shutil
from pathlib import Path

def create_requirements_txt():
    """从pyproject.toml生成requirements.txt"""
    
    print("🔧 生成requirements.txt文件...")
    
    # 基于pyproject.toml的依赖列表，但移除PostgreSQL相关依赖
    requirements = [
        "fastapi>=0.115.0",
        "uvicorn[standard]>=0.32.0", 
        "sqlalchemy>=2.0.36",
        "alembic>=1.14.0",
        # "psycopg2-binary>=2.9.10",  # 移除PostgreSQL驱动
        "pydantic>=2.10.0",
        "pydantic-settings>=2.6.0",
        "httpx>=0.28.0",
        "python-multipart>=0.0.12",
        "passlib[bcrypt]>=1.7.4",
        "python-jose[cryptography]>=3.3.0",
        "python-dotenv>=1.0.1",
        "requests>=2.32.5",
        "aiohttp>=3.12.15",
        "email-validator>=2.0.0",
    ]
    
    requirements_path = Path("zentao-mcp-backend-service/requirements.txt")
    
    with open(requirements_path, 'w', encoding='utf-8') as f:
        f.write("# Generated from pyproject.toml for Docker build\n")
        f.write("# SQLite-compatible dependencies\n\n")
        for req in requirements:
            f.write(f"{req}\n")
    
    print(f"✅ 已生成 {requirements_path}")

def create_sqlite_docker_compose():
    """创建SQLite兼容的docker-compose配置"""
    
    print("🔧 创建SQLite兼容的docker-compose配置...")
    
    sqlite_compose = """version: '3.8'

services:
  # 后端服务 - SQLite版本
  backend:
    build:
      context: ./zentao-mcp-backend-service
      dockerfile: Dockerfile
    container_name: zentao-mcp-backend-sqlite
    environment:
      - DATABASE_URL=sqlite:///./data/zentao_mcp.db
      - SECRET_KEY=your-secret-key-change-in-production
      - DEBUG=false
      - CORS_ORIGINS=http://localhost:3000
      - LOG_LEVEL=INFO
      - ZENTAO_ENV=beta
    ports:
      - "8000:8000"
    networks:
      - zentao-mcp-network
    restart: unless-stopped
    volumes:
      - backend_data:/app/data  # 持久化SQLite数据库
      - backend_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务（如果需要）
  frontend:
    build:
      context: ./zentao-mcp-frontend
      dockerfile: Dockerfile
    container_name: zentao-mcp-frontend
    ports:
      - "3000:3000"
    networks:
      - zentao-mcp-network
    depends_on:
      - backend
    restart: unless-stopped
    environment:
      - REACT_APP_API_URL=http://backend:8000

# 网络配置
networks:
  zentao-mcp-network:
    driver: bridge

# 数据卷配置
volumes:
  backend_data:
    driver: local
  backend_logs:
    driver: local
"""
    
    sqlite_compose_path = Path("docker-compose.sqlite.yml")
    
    with open(sqlite_compose_path, 'w', encoding='utf-8') as f:
        f.write(sqlite_compose)
    
    print(f"✅ 已创建 {sqlite_compose_path}")

def create_sqlite_env_example():
    """创建SQLite环境配置示例"""
    
    print("🔧 创建SQLite环境配置示例...")
    
    env_content = """# SQLite部署环境配置

# 数据库配置 - SQLite
DATABASE_URL=sqlite:///./data/zentao_mcp.db

# 应用配置
SECRET_KEY=change_me_in_production
DEBUG=false
LOG_LEVEL=INFO

# 禅道API配置
ZENTAO_ENV=beta
# ZENTAO_BASE_URL=http://your-zentao-server.com

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_EMAIL=<EMAIL>

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
"""
    
    env_path = Path(".env.sqlite")
    
    with open(env_path, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print(f"✅ 已创建 {env_path}")

def update_dockerfile_for_sqlite():
    """更新Dockerfile以支持SQLite"""
    
    print("🔧 更新Dockerfile以支持SQLite...")
    
    dockerfile_content = """# 使用Python 3.11官方镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 安装系统依赖（包括SQLite）
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    sqlite3 \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \\
    pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建数据目录
RUN mkdir -p /app/data /app/logs

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app && \\
    chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
"""
    
    dockerfile_path = Path("zentao-mcp-backend-service/Dockerfile.sqlite")
    
    with open(dockerfile_path, 'w', encoding='utf-8') as f:
        f.write(dockerfile_content)
    
    print(f"✅ 已创建 {dockerfile_path}")

def create_deployment_guide():
    """创建部署指南"""
    
    print("🔧 创建部署指南...")
    
    guide_content = """# SQLite Docker部署指南

## 🚨 问题说明

发现Docker部署配置与当前SQLite数据库不匹配：

### 问题列表
1. **数据库不匹配**: Docker配置使用PostgreSQL，但开发环境使用SQLite
2. **依赖文件缺失**: Dockerfile引用不存在的requirements.txt
3. **依赖不一致**: pyproject.toml包含PostgreSQL驱动但使用SQLite

## ✅ 修复方案

### 1. SQLite Docker部署（推荐用于开发/测试）

```bash
# 使用SQLite配置部署
docker-compose -f docker-compose.sqlite.yml --env-file .env.sqlite up -d
```

### 2. PostgreSQL Docker部署（推荐用于生产）

如果要使用PostgreSQL，需要：

1. 更新应用配置：
```bash
# 修改.env文件
DATABASE_URL=*********************************************************/zentao_mcp
```

2. 运行数据库迁移：
```bash
# 在容器内执行
docker exec -it zentao-mcp-backend alembic upgrade head
```

3. 使用原始docker-compose：
```bash
docker-compose up -d
```

## 📁 新增文件

- `requirements.txt`: Docker构建依赖文件
- `docker-compose.sqlite.yml`: SQLite兼容的Docker配置
- `Dockerfile.sqlite`: SQLite优化的Dockerfile
- `.env.sqlite`: SQLite环境配置示例

## 🔄 迁移步骤

### 从SQLite迁移到PostgreSQL：

1. 导出SQLite数据：
```bash
sqlite3 data/zentao_mcp.db .dump > backup.sql
```

2. 启动PostgreSQL服务：
```bash
docker-compose up -d database
```

3. 导入数据到PostgreSQL（需要转换SQL语法）

### 从PostgreSQL迁移到SQLite：

1. 导出PostgreSQL数据
2. 转换为SQLite兼容格式
3. 导入到SQLite数据库

## 🚀 快速开始

### 开发环境（SQLite）：
```bash
# 1. 生成requirements.txt
python fix_docker_deployment.py

# 2. 构建并启动
docker-compose -f docker-compose.sqlite.yml --env-file .env.sqlite up -d

# 3. 检查状态
docker-compose -f docker-compose.sqlite.yml ps
```

### 生产环境（PostgreSQL）：
```bash
# 1. 配置环境变量
cp .env.example .env.prod
# 编辑 .env.prod 设置PostgreSQL连接

# 2. 启动服务
docker-compose -f docker-compose.yml -f docker-compose.prod.yml --env-file .env.prod up -d
```

## ⚠️ 注意事项

1. **数据持久化**: SQLite数据库文件需要挂载到宿主机
2. **并发限制**: SQLite不适合高并发生产环境
3. **备份策略**: 定期备份SQLite数据库文件
4. **性能考虑**: 生产环境建议使用PostgreSQL
"""
    
    guide_path = Path("DOCKER_DEPLOYMENT_FIX.md")
    
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print(f"✅ 已创建 {guide_path}")

def main():
    """主修复函数"""
    print("🚀 开始修复Docker部署配置问题...")
    print("=" * 50)
    
    # 1. 生成requirements.txt
    create_requirements_txt()
    print()
    
    # 2. 创建SQLite兼容的docker-compose
    create_sqlite_docker_compose()
    print()
    
    # 3. 创建SQLite环境配置
    create_sqlite_env_example()
    print()
    
    # 4. 更新Dockerfile
    update_dockerfile_for_sqlite()
    print()
    
    # 5. 创建部署指南
    create_deployment_guide()
    print()
    
    print("=" * 50)
    print("🎉 修复完成！")
    print()
    print("📋 生成的文件:")
    print("  - zentao-mcp-backend-service/requirements.txt")
    print("  - docker-compose.sqlite.yml")
    print("  - .env.sqlite")
    print("  - zentao-mcp-backend-service/Dockerfile.sqlite")
    print("  - DOCKER_DEPLOYMENT_FIX.md")
    print()
    print("🚀 快速测试:")
    print("  docker-compose -f docker-compose.sqlite.yml --env-file .env.sqlite up -d")

if __name__ == "__main__":
    main()
