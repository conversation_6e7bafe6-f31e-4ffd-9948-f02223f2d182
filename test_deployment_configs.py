#!/usr/bin/env python3
"""
测试部署配置修复是否完整
"""

import os
import yaml
from pathlib import Path

def test_requirements_txt():
    """测试requirements.txt是否存在且正确"""
    print("🧪 测试requirements.txt...")
    
    req_path = Path("zentao-mcp-backend-service/requirements.txt")
    
    if not req_path.exists():
        print("❌ requirements.txt不存在")
        return False
    
    with open(req_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否包含必要依赖
    required_deps = [
        "fastapi",
        "uvicorn",
        "sqlalchemy",
        "pydantic"
    ]
    
    missing_deps = []
    for dep in required_deps:
        if dep not in content:
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"❌ 缺少依赖: {', '.join(missing_deps)}")
        return False
    
    # 检查是否移除了PostgreSQL依赖
    if "psycopg2" in content:
        print("⚠️  仍包含PostgreSQL依赖psycopg2")
        return False
    
    print("✅ requirements.txt正确")
    return True

def test_sqlite_dockerfile():
    """测试SQLite Dockerfile是否存在"""
    print("🧪 测试SQLite Dockerfile...")
    
    dockerfile_path = Path("zentao-mcp-backend-service/Dockerfile.sqlite")
    
    if not dockerfile_path.exists():
        print("❌ Dockerfile.sqlite不存在")
        return False
    
    with open(dockerfile_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键配置
    checks = [
        ("sqlite3", "包含SQLite3安装"),
        ("requirements.txt", "引用requirements.txt"),
        ("mkdir -p /app/data", "创建数据目录")
    ]
    
    for check, desc in checks:
        if check not in content:
            print(f"❌ {desc}缺失")
            return False
    
    print("✅ Dockerfile.sqlite正确")
    return True

def test_sqlite_compose():
    """测试SQLite docker-compose配置"""
    print("🧪 测试SQLite docker-compose配置...")
    
    compose_path = Path("docker-compose.sqlite.yml")
    
    if not compose_path.exists():
        print("❌ docker-compose.sqlite.yml不存在")
        return False
    
    try:
        with open(compose_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查服务配置
        if 'services' not in config:
            print("❌ 缺少services配置")
            return False
        
        if 'backend' not in config['services']:
            print("❌ 缺少backend服务")
            return False
        
        backend = config['services']['backend']
        
        # 检查Dockerfile引用
        if 'build' in backend and 'dockerfile' in backend['build']:
            if backend['build']['dockerfile'] != 'Dockerfile.sqlite':
                print("❌ 未使用Dockerfile.sqlite")
                return False
        
        # 检查环境变量
        if 'environment' in backend:
            env_vars = backend['environment']
            sqlite_found = False
            for env in env_vars:
                if isinstance(env, str) and 'DATABASE_URL=sqlite' in env:
                    sqlite_found = True
                    break
            
            if not sqlite_found:
                print("❌ 未配置SQLite数据库URL")
                return False
        
        # 检查数据卷
        if 'volumes' not in backend:
            print("⚠️  缺少数据卷配置")
        
        print("✅ docker-compose.sqlite.yml正确")
        return True
        
    except yaml.YAMLError as e:
        print(f"❌ YAML格式错误: {e}")
        return False

def test_sqlite_env():
    """测试SQLite环境配置"""
    print("🧪 测试SQLite环境配置...")
    
    env_path = Path(".env.sqlite")
    
    if not env_path.exists():
        print("❌ .env.sqlite不存在")
        return False
    
    with open(env_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键配置
    required_configs = [
        "DATABASE_URL=sqlite",
        "SECRET_KEY=",
        "ZENTAO_ENV=",
        "ADMIN_USERNAME=",
        "ADMIN_PASSWORD="
    ]
    
    missing_configs = []
    for config in required_configs:
        if config not in content:
            missing_configs.append(config)
    
    if missing_configs:
        print(f"❌ 缺少配置: {', '.join(missing_configs)}")
        return False
    
    print("✅ .env.sqlite正确")
    return True

def test_deploy_script():
    """测试部署脚本"""
    print("🧪 测试部署脚本...")
    
    script_path = Path("deploy-sqlite.sh")
    
    if not script_path.exists():
        print("❌ deploy-sqlite.sh不存在")
        return False
    
    # 检查执行权限
    if not os.access(script_path, os.X_OK):
        print("❌ deploy-sqlite.sh没有执行权限")
        return False
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键功能
    required_functions = [
        "build_images",
        "deploy_services", 
        "show_status",
        "docker-compose.sqlite.yml"
    ]
    
    missing_functions = []
    for func in required_functions:
        if func not in content:
            missing_functions.append(func)
    
    if missing_functions:
        print(f"❌ 缺少功能: {', '.join(missing_functions)}")
        return False
    
    print("✅ deploy-sqlite.sh正确")
    return True

def test_documentation():
    """测试文档完整性"""
    print("🧪 测试文档完整性...")
    
    docs = [
        ("DOCKER_DEPLOYMENT_FIX.md", "Docker部署修复指南"),
        ("DEPLOYMENT_COMPARISON.md", "部署方式对比")
    ]
    
    all_exist = True
    for doc_path, desc in docs:
        path = Path(doc_path)
        if not path.exists():
            print(f"❌ {desc}不存在: {doc_path}")
            all_exist = False
        else:
            print(f"✅ {desc}存在")
    
    return all_exist

def test_original_configs_intact():
    """测试原始配置是否保持完整"""
    print("🧪 测试原始配置完整性...")
    
    original_files = [
        "docker-compose.yml",
        "docker-compose.dev.yml", 
        "docker-compose.prod.yml",
        "deploy.sh",
        "deploy-universal.sh"
    ]
    
    all_intact = True
    for file_path in original_files:
        path = Path(file_path)
        if not path.exists():
            print(f"⚠️  原始文件缺失: {file_path}")
            all_intact = False
        else:
            print(f"✅ 原始文件完整: {file_path}")
    
    return all_intact

def main():
    """主测试函数"""
    print("🚀 测试部署配置修复完整性")
    print("=" * 50)
    
    tests = [
        ("requirements.txt", test_requirements_txt),
        ("SQLite Dockerfile", test_sqlite_dockerfile),
        ("SQLite Compose", test_sqlite_compose),
        ("SQLite环境配置", test_sqlite_env),
        ("部署脚本", test_deploy_script),
        ("文档完整性", test_documentation),
        ("原始配置完整性", test_original_configs_intact)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("-" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！部署配置修复完整")
        print("\n🚀 可以开始使用SQLite部署:")
        print("  ./deploy-sqlite.sh deploy dev")
    else:
        print(f"\n⚠️  {total-passed} 个测试失败，请检查修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
